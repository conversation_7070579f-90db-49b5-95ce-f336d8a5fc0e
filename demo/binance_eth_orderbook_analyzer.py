"""
Binance ETH Order Book Analyzer
获取Binance ETH 5分钟订单薄数据并计算市场微观结构指标
"""

import requests
import json
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
import sys
import os

# Add parent directory to path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from market_microstructure_indicators import MarketMicrostructureIndicators

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BinanceOrderBookAnalyzer:
    """
    Binance订单薄数据获取和分析器
    """
    
    def __init__(self, symbol: str = "ETHUSDT"):
        self.symbol = symbol
        self.base_url = "https://api.binance.com"
        self.calculator = MarketMicrostructureIndicators()
        
        # Data storage
        self.order_book_history = []
        self.indicators_history = []
        
        # Configuration
        self.depth_limit = 20  # 获取20档深度数据
        self.update_interval = 300  # 5分钟 = 300秒
        
    def get_order_book_snapshot(self) -> Optional[Dict]:
        """
        获取订单薄快照数据
        
        Returns:
            订单薄数据或None（如果失败）
        """
        try:
            url = f"{self.base_url}/api/v3/depth"
            params = {
                'symbol': self.symbol,
                'limit': self.depth_limit
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            # 格式化数据
            order_book = {
                'timestamp': datetime.now().isoformat(),
                'lastUpdateId': data['lastUpdateId'],
                'bids': [(float(bid[0]), float(bid[1])) for bid in data['bids']],
                'asks': [(float(ask[0]), float(ask[1])) for ask in data['asks']]
            }
            
            logger.info(f"获取订单薄数据成功: {len(order_book['bids'])} bids, {len(order_book['asks'])} asks")
            return order_book
            
        except requests.exceptions.RequestException as e:
            logger.error(f"获取订单薄数据失败: {e}")
            return None
        except Exception as e:
            logger.error(f"处理订单薄数据时出错: {e}")
            return None
    
    def get_24hr_ticker(self) -> Optional[Dict]:
        """
        获取24小时价格统计
        
        Returns:
            价格统计数据
        """
        try:
            url = f"{self.base_url}/api/v3/ticker/24hr"
            params = {'symbol': self.symbol}
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"获取价格统计失败: {e}")
            return None
    
    def calculate_indicators(self, order_book: Dict) -> Dict:
        """
        计算市场微观结构指标
        
        Args:
            order_book: 订单薄数据
            
        Returns:
            计算得到的指标
        """
        try:
            # 准备数据格式
            formatted_book = {
                'bids': order_book['bids'],
                'asks': order_book['asks']
            }
            
            # 计算所有指标
            indicators = self.calculator.process_order_book_snapshot(
                formatted_book, 
                order_book['timestamp']
            )
            
            # 添加额外信息
            indicators['symbol'] = self.symbol
            indicators['lastUpdateId'] = order_book['lastUpdateId']
            
            return indicators
            
        except Exception as e:
            logger.error(f"计算指标时出错: {e}")
            return {}
    
    def analyze_current_market(self) -> Dict:
        """
        分析当前市场状况
        
        Returns:
            市场分析结果
        """
        # 获取订单薄数据
        order_book = self.get_order_book_snapshot()
        if not order_book:
            return {}
        
        # 获取价格统计
        ticker = self.get_24hr_ticker()
        
        # 计算指标
        indicators = self.calculate_indicators(order_book)
        
        # 添加价格统计信息
        if ticker:
            indicators.update({
                'price_24h_change': float(ticker.get('priceChangePercent', 0)),
                'volume_24h': float(ticker.get('volume', 0)),
                'high_24h': float(ticker.get('highPrice', 0)),
                'low_24h': float(ticker.get('lowPrice', 0)),
                'weighted_avg_price': float(ticker.get('weightedAvgPrice', 0))
            })
        
        # 存储数据
        self.order_book_history.append(order_book)
        self.indicators_history.append(indicators)
        
        return indicators
    
    def continuous_monitoring(self, duration_minutes: int = 60, save_data: bool = True):
        """
        持续监控订单薄数据
        
        Args:
            duration_minutes: 监控时长（分钟）
            save_data: 是否保存数据
        """
        logger.info(f"开始监控 {self.symbol} 订单薄数据，时长: {duration_minutes} 分钟")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        try:
            while datetime.now() < end_time:
                # 分析当前市场
                indicators = self.analyze_current_market()
                
                if indicators:
                    self.print_current_status(indicators)
                
                # 等待下一次更新
                logger.info(f"等待 {self.update_interval} 秒后下次更新...")
                time.sleep(self.update_interval)
            
            logger.info("监控完成")
            
            # 保存数据
            if save_data and self.indicators_history:
                self.save_results()
                
        except KeyboardInterrupt:
            logger.info("用户中断监控")
            if save_data and self.indicators_history:
                self.save_results()
        except Exception as e:
            logger.error(f"监控过程中出错: {e}")
    
    def print_current_status(self, indicators: Dict):
        """
        打印当前市场状态
        
        Args:
            indicators: 指标数据
        """
        print("\n" + "="*60)
        print(f"时间: {indicators.get('timestamp', 'N/A')}")
        print(f"交易对: {indicators.get('symbol', 'N/A')}")
        print("-"*60)
        
        # 基础价格信息
        if 'best_bid' in indicators and 'best_ask' in indicators:
            print(f"最佳买价: {indicators['best_bid']:.4f}")
            print(f"最佳卖价: {indicators['best_ask']:.4f}")
            print(f"中间价: {indicators.get('mid_price', 0):.4f}")
        
        # 价差指标
        if 'bid_ask_spread_abs' in indicators:
            print(f"绝对价差: {indicators['bid_ask_spread_abs']:.4f}")
        if 'bid_ask_spread_pct' in indicators:
            print(f"相对价差: {indicators['bid_ask_spread_pct']:.4f}%")
        
        # 流动性指标
        if 'liquidity_imbalance_volume' in indicators:
            imbalance = indicators['liquidity_imbalance_volume']
            print(f"流动性不平衡: {imbalance:.4f} {'(买方优势)' if imbalance > 0 else '(卖方优势)' if imbalance < 0 else '(平衡)'}")
        
        # 深度斜率
        if 'bid_slope' in indicators and not np.isnan(indicators['bid_slope']):
            print(f"买单深度斜率: {indicators['bid_slope']:.2f}")
        if 'ask_slope' in indicators and not np.isnan(indicators['ask_slope']):
            print(f"卖单深度斜率: {indicators['ask_slope']:.2f}")
        
        # 订单薄压力
        if 'total_bid_volume' in indicators:
            print(f"总买单量: {indicators['total_bid_volume']:.2f}")
        if 'total_ask_volume' in indicators:
            print(f"总卖单量: {indicators['total_ask_volume']:.2f}")
        
        # 24小时统计
        if 'price_24h_change' in indicators:
            change = indicators['price_24h_change']
            print(f"24h涨跌: {change:.2f}%")
        if 'volume_24h' in indicators:
            print(f"24h成交量: {indicators['volume_24h']:.2f}")
        
        print("="*60)
    
    def save_results(self):
        """
        保存分析结果
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存指标数据
            if self.indicators_history:
                df_indicators = pd.DataFrame(self.indicators_history)
                filename_indicators = f"demo/eth_orderbook_indicators_{timestamp}.csv"
                df_indicators.to_csv(filename_indicators, index=False)
                logger.info(f"指标数据已保存到: {filename_indicators}")
            
            # 保存订单薄原始数据（JSON格式）
            if self.order_book_history:
                filename_orderbook = f"demo/eth_orderbook_raw_{timestamp}.json"
                with open(filename_orderbook, 'w', encoding='utf-8') as f:
                    json.dump(self.order_book_history, f, indent=2, ensure_ascii=False)
                logger.info(f"订单薄原始数据已保存到: {filename_orderbook}")
            
            # 生成分析报告
            self.generate_analysis_report(timestamp)
            
        except Exception as e:
            logger.error(f"保存数据时出错: {e}")
    
    def generate_analysis_report(self, timestamp: str):
        """
        生成分析报告
        
        Args:
            timestamp: 时间戳
        """
        try:
            if not self.indicators_history:
                return
            
            df = pd.DataFrame(self.indicators_history)
            
            report = []
            report.append("ETH订单薄分析报告")
            report.append("="*50)
            report.append(f"分析时间: {timestamp}")
            report.append(f"数据点数: {len(df)}")
            report.append("")
            
            # 价差统计
            if 'bid_ask_spread_pct' in df.columns:
                spread_data = df['bid_ask_spread_pct'].dropna()
                if len(spread_data) > 0:
                    report.append("价差统计:")
                    report.append(f"  平均价差: {spread_data.mean():.4f}%")
                    report.append(f"  价差标准差: {spread_data.std():.4f}%")
                    report.append(f"  最小价差: {spread_data.min():.4f}%")
                    report.append(f"  最大价差: {spread_data.max():.4f}%")
                    report.append("")
            
            # 流动性不平衡统计
            if 'liquidity_imbalance_volume' in df.columns:
                imbalance_data = df['liquidity_imbalance_volume'].dropna()
                if len(imbalance_data) > 0:
                    report.append("流动性不平衡统计:")
                    report.append(f"  平均不平衡: {imbalance_data.mean():.4f}")
                    report.append(f"  不平衡标准差: {imbalance_data.std():.4f}")
                    report.append(f"  买方优势次数: {(imbalance_data > 0.1).sum()}")
                    report.append(f"  卖方优势次数: {(imbalance_data < -0.1).sum()}")
                    report.append("")
            
            # 深度斜率统计
            if 'bid_slope' in df.columns and 'ask_slope' in df.columns:
                bid_slope_data = df['bid_slope'].dropna()
                ask_slope_data = df['ask_slope'].dropna()
                
                if len(bid_slope_data) > 0:
                    report.append("深度斜率统计:")
                    report.append(f"  买单平均斜率: {bid_slope_data.mean():.2f}")
                    report.append(f"  卖单平均斜率: {ask_slope_data.mean():.2f}")
                    report.append("")
            
            # 保存报告
            filename_report = f"demo/eth_orderbook_report_{timestamp}.txt"
            with open(filename_report, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report))
            
            logger.info(f"分析报告已保存到: {filename_report}")
            
            # 打印报告摘要
            print("\n" + '\n'.join(report))
            
        except Exception as e:
            logger.error(f"生成分析报告时出错: {e}")


def main():
    """
    主函数
    """
    print("Binance ETH 订单薄分析器")
    print("="*50)
    
    # 创建分析器
    analyzer = BinanceOrderBookAnalyzer("ETHUSDT")
    
    # 选择运行模式
    print("请选择运行模式:")
    print("1. 单次分析")
    print("2. 持续监控 (默认60分钟)")
    print("3. 自定义持续监控")
    
    try:
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == "1":
            # 单次分析
            print("\n执行单次分析...")
            indicators = analyzer.analyze_current_market()
            
            if indicators:
                analyzer.print_current_status(indicators)
                
                # 保存单次分析结果
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"demo/eth_single_analysis_{timestamp}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(indicators, f, indent=2, ensure_ascii=False, default=str)
                print(f"\n分析结果已保存到: {filename}")
            else:
                print("分析失败，请检查网络连接")
                
        elif choice == "2":
            # 默认60分钟监控
            analyzer.continuous_monitoring(duration_minutes=60)
            
        elif choice == "3":
            # 自定义监控时长
            duration = int(input("请输入监控时长（分钟）: "))
            analyzer.continuous_monitoring(duration_minutes=duration)
            
        else:
            print("无效选择，执行单次分析...")
            indicators = analyzer.analyze_current_market()
            if indicators:
                analyzer.print_current_status(indicators)
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        print(f"错误: {e}")


if __name__ == "__main__":
    main()