# Binance ETH 订单薄市场微观结构分析

本目录包含用于获取和分析Binance ETH订单薄数据的工具，专门计算市场微观结构指标。

## 功能特性

### 核心指标
- **Bid-Ask Spread (买卖价差)**: 绝对价差、相对价差、百分比价差
- **Depth Slope (深度斜率)**: 买单和卖单的深度斜率分析
- **Liquidity Imbalance (流动性不平衡)**: 买卖双方流动性不平衡度量

### 扩展指标
- 价格影响分析 (1000 ETH, 5000 ETH交易的价格影响)
- 加权中间价
- 订单薄压力指标
- 多档深度分析

## 文件说明

### 主要脚本

1. **`eth_5m_microstructure.py`** - 主要的5分钟数据采集器
   - 支持单次采集和持续监控
   - 自动计算所有微观结构指标
   - 生成详细的分析报告

2. **`binance_eth_orderbook_analyzer.py`** - 通用订单薄分析器
   - 支持自定义时间间隔
   - 包含24小时价格统计
   - 实时状态显示

3. **`historical_orderbook_collector.py`** - 历史数据收集器
   - 持续收集实时数据构建历史数据集
   - 从K线数据估算订单薄特征
   - 数据库存储和管理
   - 构建训练数据集

4. **`training_data_processor.py`** - 训练数据处理器
   - 特征工程和数据预处理
   - 创建时间特征、技术指标、滞后特征
   - 数据清理和标准化
   - 生成目标变量

5. **`orderbook_ml_trainer.py`** - 机器学习模型训练器
   - 支持多种ML算法 (RF, XGBoost, LightGBM等)
   - 回归和分类任务
   - 模型性能评估和比较
   - 自动保存最佳模型

6. **`test_eth_orderbook.py`** - 测试脚本
   - 验证数据获取功能
   - 快速测试网络连接

## 使用方法

### 1. 实时数据采集

```bash
# 单次数据采集
python demo/eth_5m_microstructure.py
# 选择选项 1 进行单次采集

# 持续监控（推荐）
python demo/eth_5m_microstructure.py
# 选择选项 2: 1小时采集 (12个数据点)
# 选择选项 3: 4小时采集 (48个数据点)
# 选择选项 4: 自定义时长
```

### 2. 历史数据收集（用于训练）

```bash
# 收集实时数据构建训练集
python demo/historical_orderbook_collector.py
# 选择选项 1: 开始收集实时数据 (建议24-72小时)
# 选择选项 2: 构建训练数据集
# 选择选项 3: 获取历史K线数据并估算特征
# 选择选项 4: 综合构建训练数据集
```

### 3. 数据预处理和特征工程

```bash
# 处理训练数据，创建特征
python demo/training_data_processor.py
# 输入: 原始训练数据文件
# 输出: 处理后的训练数据 + 特征报告
```

### 4. 机器学习模型训练

```bash
# 训练预测模型
python demo/orderbook_ml_trainer.py
# 输入: 处理后的训练数据
# 输出: 训练好的模型 + 性能报告
```

### 5. 快速测试

```bash
python demo/test_eth_orderbook.py
```

## 输出文件

### 数据文件
- `eth_5m_microstructure_YYYYMMDD_HHMMSS_final.csv` - CSV格式的指标数据
- `eth_5m_microstructure_YYYYMMDD_HHMMSS_final.json` - JSON格式的原始数据
- `eth_5m_microstructure_report_YYYYMMDD_HHMMSS.txt` - 分析报告

### 日志文件
- `eth_5m_microstructure.log` - 运行日志

## 数据字段说明

### 基础价格信息
- `best_bid_price`: 最佳买价
- `best_ask_price`: 最佳卖价
- `mid_price`: 中间价

### 价差指标
- `bid_ask_spread_abs`: 绝对价差
- `bid_ask_spread_rel`: 相对价差
- `bid_ask_spread_pct`: 百分比价差

### 流动性指标
- `liquidity_imbalance_volume`: 基于成交量的流动性不平衡
- `liquidity_imbalance_orders`: 基于订单数量的流动性不平衡
- `total_bid_volume`: 总买单量
- `total_ask_volume`: 总卖单量

### 深度分析
- `bid_slope`: 买单深度斜率
- `ask_slope`: 卖单深度斜率
- `bid_volume_top5`: 前5档买单总量
- `ask_volume_top5`: 前5档卖单总量

### 价格影响
- `price_impact_1000_buy`: 1000 ETH买入的价格影响 (%)
- `price_impact_1000_sell`: 1000 ETH卖出的价格影响 (%)
- `price_impact_5000_buy`: 5000 ETH买入的价格影响 (%)
- `price_impact_5000_sell`: 5000 ETH卖出的价格影响 (%)

## 配置参数

### 可调整参数
- `depth_limit`: 订单薄深度档数 (默认: 20)
- `interval_seconds`: 采集间隔 (默认: 300秒 = 5分钟)
- `save_interval`: 保存间隔 (默认: 每6个数据点)

### 修改配置
在脚本中找到以下部分进行修改：
```python
# 配置参数
self.depth_limit = 20  # 获取20档深度
self.interval_seconds = 300  # 5分钟 = 300秒
```

## 数据解读

### 价差分析
- 价差越小，市场流动性越好
- 正常情况下ETH价差通常在0.001%-0.01%之间

### 流动性不平衡
- 正值表示买方流动性更强
- 负值表示卖方流动性更强
- 绝对值越大，不平衡程度越高

### 深度斜率
- 负值表示价格越远离最佳价格，累积量越大
- 斜率绝对值越大，深度越好

### 价格影响
- 表示大额交易对价格的影响程度
- 值越小，市场深度越好

## 注意事项

1. **网络要求**: 需要稳定的网络连接访问Binance API
2. **API限制**: Binance有API调用频率限制，5分钟间隔是安全的
3. **数据存储**: 长时间运行会产生大量数据，注意磁盘空间
4. **时区**: 所有时间戳使用本地时区

## 故障排除

### 常见问题

1. **网络连接失败**
   ```
   解决方案: 检查网络连接，确认可以访问api.binance.com
   ```

2. **导入模块失败**
   ```
   解决方案: 确保在项目根目录运行，或检查Python路径设置
   ```

3. **数据保存失败**
   ```
   解决方案: 检查demo目录的写入权限
   ```

## 扩展功能

### 添加其他交易对
修改脚本中的symbol参数：
```python
self.symbol = "BTCUSDT"  # 改为其他交易对
```

### 调整采集频率
修改interval_seconds参数：
```python
self.interval_seconds = 60  # 改为1分钟采集
```

### 添加自定义指标
在`calculate_microstructure_indicators`方法中添加：
```python
# 添加自定义计算
indicators['custom_indicator'] = your_calculation()
```

## 示例输出

```
==============================================================
时间: 2025-09-07T11:13:50.597000
数据点: 1
==============================================================
最佳买价: 4295.8800
最佳卖价: 4295.8900
中间价: 4295.8850
价差: 0.0002%
流动性不平衡: 0.6506 (买方优势)
买单深度斜率: -8.20
卖单深度斜率: 14.27
1000 ETH买入价格影响: 0.0156%
1000 ETH卖出价格影响: 0.0134%
==============================================================
```

## 技术支持

如有问题或建议，请检查：
1. 网络连接状态
2. Python环境和依赖包
3. Binance API状态
4. 日志文件中的错误信息
## 完整的
训练流程

### 步骤1: 收集历史数据
```bash
# 开始收集实时数据（建议运行24-72小时）
python demo/historical_orderbook_collector.py
# 选择选项 1，输入收集时长（小时）
```

### 步骤2: 构建训练数据集
```bash
# 构建综合训练数据集
python demo/historical_orderbook_collector.py
# 选择选项 4: 综合构建训练数据集
```

### 步骤3: 数据预处理
```bash
# 特征工程和数据清理
python demo/training_data_processor.py
# 输入刚才生成的训练数据集文件路径
```

### 步骤4: 模型训练
```bash
# 训练机器学习模型
python demo/orderbook_ml_trainer.py
# 输入处理后的训练数据文件路径
# 选择预测任务和训练模式
```

## 训练数据说明

### 数据来源
1. **实时订单薄数据**: 通过Binance API实时收集的Level 2订单薄快照
2. **历史K线数据**: 从Binance获取的历史K线数据，用于估算订单薄特征

### 特征类型
- **时间特征**: 小时、分钟、星期几等时间相关特征
- **技术指标**: 移动平均、标准差、最值等统计特征
- **滞后特征**: 历史时刻的指标值
- **变化特征**: 指标的变化率和差分
- **交互特征**: 不同指标之间的交互项

### 预测任务
1. **价差预测**: 预测未来的买卖价差变化
2. **流动性不平衡预测**: 预测买卖双方流动性不平衡
3. **价差方向预测**: 预测价差是扩大还是缩小
4. **市场状态预测**: 预测市场微观结构状态

### 支持的ML算法
- **回归**: Linear Regression, Random Forest, XGBoost, LightGBM, SVR
- **分类**: Logistic Regression, Random Forest, XGBoost, LightGBM, SVC

## 数据库结构

系统使用SQLite数据库存储历史数据：

### orderbook_snapshots表
- `timestamp`: 时间戳
- `symbol`: 交易对
- `best_bid/best_ask`: 最佳买卖价
- `bid_ask_spread_pct`: 价差百分比
- `liquidity_imbalance`: 流动性不平衡
- `bid_slope/ask_slope`: 深度斜率
- `total_bid_volume/total_ask_volume`: 总买卖量
- `price_impact_*`: 价格影响指标
- `raw_data`: 原始订单薄JSON数据

### kline_data表
- K线历史数据，用于特征估算

## 性能优化建议

### 数据收集
- 建议收集至少24小时的连续数据
- 可以并行运行多个收集器收集不同交易对
- 定期清理过期数据以节省存储空间

### 特征工程
- 根据具体预测任务选择相关特征
- 使用特征选择技术减少维度
- 考虑特征的时间稳定性

### 模型训练
- 使用时间序列交叉验证
- 注意避免数据泄露
- 定期重新训练模型以适应市场变化

## 实际应用示例

### 高频交易策略
```python
# 基于价差预测的策略
if predicted_spread_change < -0.001:  # 价差将缩小
    # 考虑进入做市策略
    pass
elif predicted_spread_change > 0.002:  # 价差将扩大
    # 考虑退出做市策略
    pass
```

### 流动性提供策略
```python
# 基于流动性不平衡预测
if predicted_imbalance > 0.3:  # 买方流动性强
    # 在卖方提供流动性
    pass
elif predicted_imbalance < -0.3:  # 卖方流动性强
    # 在买方提供流动性
    pass
```

### 风险管理
```python
# 基于市场状态预测
if predicted_market_state == 3:  # 高价差+不平衡
    # 降低仓位，提高风险控制
    pass
elif predicted_market_state == 0:  # 正常状态
    # 正常交易策略
    pass
```