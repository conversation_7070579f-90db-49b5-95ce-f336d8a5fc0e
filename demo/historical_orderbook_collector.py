"""
历史订单薄数据收集器
用于获取和构建训练数据集
"""

import requests
import json
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os
from typing import Dict, List, Optional, Tuple
import sqlite3
import pickle

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from market_microstructure_indicators import MarketMicrostructureIndicators

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('demo/historical_collection.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class HistoricalOrderBookCollector:
    """
    历史订单薄数据收集器
    
    注意：由于Binance不提供历史订单薄API，这个类提供以下功能：
    1. 持续收集实时数据构建历史数据集
    2. 从已有的K线数据推断订单薄特征
    3. 数据存储和管理
    """
    
    def __init__(self, symbol: str = "ETHUSDT"):
        self.symbol = symbol
        self.base_url = "https://api.binance.com"
        self.calculator = MarketMicrostructureIndicators()
        
        # 数据库设置
        self.db_path = "demo/orderbook_history.db"
        self.init_database()
        
        # 配置
        self.depth_limit = 20
        self.collection_interval = 60  # 1分钟收集一次，更密集
        
    def init_database(self):
        """
        初始化数据库
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建订单薄快照表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS orderbook_snapshots (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    best_bid REAL,
                    best_ask REAL,
                    bid_ask_spread_pct REAL,
                    liquidity_imbalance REAL,
                    bid_slope REAL,
                    ask_slope REAL,
                    total_bid_volume REAL,
                    total_ask_volume REAL,
                    price_impact_1000_buy REAL,
                    price_impact_1000_sell REAL,
                    raw_data TEXT
                )
            ''')
            
            # 创建K线数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS kline_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    open_price REAL,
                    high_price REAL,
                    low_price REAL,
                    close_price REAL,
                    volume REAL,
                    quote_volume REAL,
                    trades_count INTEGER,
                    interval_type TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
    
    def get_historical_klines(self, interval: str = "5m", limit: int = 1000) -> List[Dict]:
        """
        获取历史K线数据
        
        Args:
            interval: 时间间隔 (1m, 5m, 15m, 1h, 1d)
            limit: 获取数量 (最大1000)
        """
        try:
            url = f"{self.base_url}/api/v3/klines"
            params = {
                'symbol': self.symbol,
                'interval': interval,
                'limit': limit
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            klines = response.json()
            
            formatted_klines = []
            for kline in klines:
                formatted_klines.append({
                    'timestamp': datetime.fromtimestamp(kline[0] / 1000).isoformat(),
                    'open': float(kline[1]),
                    'high': float(kline[2]),
                    'low': float(kline[3]),
                    'close': float(kline[4]),
                    'volume': float(kline[5]),
                    'close_time': datetime.fromtimestamp(kline[6] / 1000).isoformat(),
                    'quote_volume': float(kline[7]),
                    'trades_count': int(kline[8]),
                    'taker_buy_base_volume': float(kline[9]),
                    'taker_buy_quote_volume': float(kline[10])
                })
            
            logger.info(f"获取到 {len(formatted_klines)} 条K线数据")
            return formatted_klines
            
        except Exception as e:
            logger.error(f"获取K线数据失败: {e}")
            return []
    
    def estimate_orderbook_from_kline(self, kline_data: Dict) -> Dict:
        """
        从K线数据估算订单薄特征
        
        这是一个近似方法，基于价格波动和成交量来估算订单薄特征
        """
        try:
            open_price = kline_data['open']
            high_price = kline_data['high']
            low_price = kline_data['low']
            close_price = kline_data['close']
            volume = kline_data['volume']
            trades_count = kline_data['trades_count']
            
            # 估算价差（基于价格波动）
            price_range = high_price - low_price
            estimated_spread_pct = (price_range / close_price) * 0.1  # 假设价差是价格范围的10%
            
            # 估算流动性不平衡（基于价格变化方向）
            price_change = close_price - open_price
            estimated_imbalance = np.tanh(price_change / open_price * 100)  # 标准化到[-1,1]
            
            # 估算深度（基于成交量）
            avg_trade_size = volume / trades_count if trades_count > 0 else 0
            estimated_depth = volume * 0.5  # 假设订单薄深度是成交量的50%
            
            # 估算价格影响（基于波动率）
            volatility = price_range / open_price
            estimated_price_impact = volatility * 10  # 简化估算
            
            return {
                'timestamp': kline_data['timestamp'],
                'estimated_spread_pct': estimated_spread_pct,
                'estimated_imbalance': estimated_imbalance,
                'estimated_depth': estimated_depth,
                'estimated_price_impact': estimated_price_impact,
                'price_volatility': volatility,
                'volume_intensity': volume / 300,  # 5分钟标准化
                'trade_intensity': trades_count / 300,
                'avg_trade_size': avg_trade_size,
                'price_trend': 1 if price_change > 0 else -1 if price_change < 0 else 0
            }
            
        except Exception as e:
            logger.error(f"估算订单薄特征失败: {e}")
            return {}
    
    def collect_realtime_for_training(self, duration_hours: int = 24):
        """
        收集实时数据用于训练
        
        Args:
            duration_hours: 收集时长（小时）
        """
        logger.info(f"开始收集实时训练数据，时长: {duration_hours} 小时")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(hours=duration_hours)
        
        collected_count = 0
        
        try:
            while datetime.now() < end_time:
                # 获取当前订单薄
                order_book = self.fetch_current_orderbook()
                
                if order_book:
                    # 计算指标
                    indicators = self.calculate_indicators(order_book)
                    
                    # 存储到数据库
                    self.store_orderbook_snapshot(indicators, order_book)
                    collected_count += 1
                    
                    if collected_count % 10 == 0:
                        logger.info(f"已收集 {collected_count} 个数据点")
                
                # 等待下次收集
                time.sleep(self.collection_interval)
            
            logger.info(f"实时数据收集完成，共收集 {collected_count} 个数据点")
            
        except KeyboardInterrupt:
            logger.info("用户中断收集")
        except Exception as e:
            logger.error(f"收集过程出错: {e}")
    
    def fetch_current_orderbook(self) -> Optional[Dict]:
        """
        获取当前订单薄
        """
        try:
            url = f"{self.base_url}/api/v3/depth"
            params = {
                'symbol': self.symbol,
                'limit': self.depth_limit
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            return {
                'timestamp': datetime.now(),
                'bids': [(float(bid[0]), float(bid[1])) for bid in data['bids']],
                'asks': [(float(ask[0]), float(ask[1])) for ask in data['asks']],
                'lastUpdateId': data['lastUpdateId']
            }
            
        except Exception as e:
            logger.error(f"获取订单薄失败: {e}")
            return None
    
    def calculate_indicators(self, order_book: Dict) -> Dict:
        """
        计算指标
        """
        try:
            formatted_book = {
                'bids': order_book['bids'],
                'asks': order_book['asks']
            }
            
            indicators = self.calculator.process_order_book_snapshot(
                formatted_book, 
                order_book['timestamp'].isoformat()
            )
            
            return indicators
            
        except Exception as e:
            logger.error(f"计算指标失败: {e}")
            return {}
    
    def store_orderbook_snapshot(self, indicators: Dict, raw_orderbook: Dict):
        """
        存储订单薄快照到数据库
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO orderbook_snapshots (
                    timestamp, symbol, best_bid, best_ask, bid_ask_spread_pct,
                    liquidity_imbalance, bid_slope, ask_slope, total_bid_volume,
                    total_ask_volume, price_impact_1000_buy, price_impact_1000_sell,
                    raw_data
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                indicators.get('timestamp'),
                self.symbol,
                indicators.get('best_bid'),
                indicators.get('best_ask'),
                indicators.get('bid_ask_spread_pct'),
                indicators.get('liquidity_imbalance_volume'),
                indicators.get('bid_slope'),
                indicators.get('ask_slope'),
                indicators.get('total_bid_volume'),
                indicators.get('total_ask_volume'),
                indicators.get('price_impact_1000_buy'),
                indicators.get('price_impact_1000_sell'),
                json.dumps(raw_orderbook, default=str)
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"存储数据失败: {e}")
    
    def build_training_dataset(self, days_back: int = 7) -> pd.DataFrame:
        """
        构建训练数据集
        
        Args:
            days_back: 使用多少天前的数据
        """
        try:
            # 1. 从数据库获取实时收集的数据
            conn = sqlite3.connect(self.db_path)
            
            cutoff_date = (datetime.now() - timedelta(days=days_back)).isoformat()
            
            query = '''
                SELECT * FROM orderbook_snapshots 
                WHERE timestamp >= ? 
                ORDER BY timestamp
            '''
            
            df_realtime = pd.read_sql_query(query, conn, params=(cutoff_date,))
            conn.close()
            
            logger.info(f"从数据库获取到 {len(df_realtime)} 条实时数据")
            
            # 2. 获取历史K线数据并估算特征
            historical_klines = self.get_historical_klines(interval="5m", limit=1000)
            
            estimated_features = []
            for kline in historical_klines:
                features = self.estimate_orderbook_from_kline(kline)
                if features:
                    estimated_features.append(features)
            
            df_estimated = pd.DataFrame(estimated_features)
            logger.info(f"从K线数据估算到 {len(df_estimated)} 条特征")
            
            # 3. 合并数据集
            if len(df_realtime) > 0 and len(df_estimated) > 0:
                # 标准化列名
                df_realtime['data_source'] = 'realtime'
                df_estimated['data_source'] = 'estimated'
                
                # 选择共同特征进行合并
                common_features = ['timestamp']
                
                # 创建统一的训练数据集
                training_data = []
                
                # 添加实时数据
                for _, row in df_realtime.iterrows():
                    training_data.append({
                        'timestamp': row['timestamp'],
                        'spread_pct': row.get('bid_ask_spread_pct', np.nan),
                        'liquidity_imbalance': row.get('liquidity_imbalance', np.nan),
                        'bid_slope': row.get('bid_slope', np.nan),
                        'ask_slope': row.get('ask_slope', np.nan),
                        'total_volume': (row.get('total_bid_volume', 0) + row.get('total_ask_volume', 0)),
                        'price_impact': row.get('price_impact_1000_buy', np.nan),
                        'data_source': 'realtime'
                    })
                
                # 添加估算数据
                for _, row in df_estimated.iterrows():
                    training_data.append({
                        'timestamp': row['timestamp'],
                        'spread_pct': row.get('estimated_spread_pct', np.nan),
                        'liquidity_imbalance': row.get('estimated_imbalance', np.nan),
                        'bid_slope': np.nan,  # K线数据无法估算
                        'ask_slope': np.nan,
                        'total_volume': row.get('estimated_depth', np.nan),
                        'price_impact': row.get('estimated_price_impact', np.nan),
                        'data_source': 'estimated',
                        'price_volatility': row.get('price_volatility', np.nan),
                        'volume_intensity': row.get('volume_intensity', np.nan),
                        'trade_intensity': row.get('trade_intensity', np.nan),
                        'price_trend': row.get('price_trend', 0)
                    })
                
                df_training = pd.DataFrame(training_data)
                df_training['timestamp'] = pd.to_datetime(df_training['timestamp'])
                df_training = df_training.sort_values('timestamp').reset_index(drop=True)
                
                logger.info(f"构建训练数据集完成，共 {len(df_training)} 条记录")
                return df_training
            
            elif len(df_realtime) > 0:
                logger.info("仅使用实时数据构建训练集")
                return df_realtime
            
            elif len(df_estimated) > 0:
                logger.info("仅使用估算数据构建训练集")
                return df_estimated
            
            else:
                logger.warning("没有可用的训练数据")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"构建训练数据集失败: {e}")
            return pd.DataFrame()
    
    def save_training_dataset(self, df: pd.DataFrame, filename: str = None):
        """
        保存训练数据集
        """
        try:
            if df.empty:
                logger.warning("数据集为空，无法保存")
                return
            
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"demo/training_dataset_{self.symbol}_{timestamp}.csv"
            
            df.to_csv(filename, index=False)
            logger.info(f"训练数据集已保存到: {filename}")
            
            # 同时保存为pickle格式，保持数据类型
            pickle_filename = filename.replace('.csv', '.pkl')
            df.to_pickle(pickle_filename)
            logger.info(f"训练数据集(pickle)已保存到: {pickle_filename}")
            
            # 生成数据集统计报告
            self.generate_dataset_report(df, filename.replace('.csv', '_report.txt'))
            
        except Exception as e:
            logger.error(f"保存训练数据集失败: {e}")
    
    def generate_dataset_report(self, df: pd.DataFrame, report_filename: str):
        """
        生成数据集报告
        """
        try:
            report = []
            report.append("训练数据集统计报告")
            report.append("="*50)
            report.append(f"生成时间: {datetime.now().isoformat()}")
            report.append(f"数据集大小: {len(df)} 条记录")
            report.append(f"时间跨度: {df['timestamp'].min()} 到 {df['timestamp'].max()}")
            report.append("")
            
            # 数据源统计
            if 'data_source' in df.columns:
                source_counts = df['data_source'].value_counts()
                report.append("数据源分布:")
                for source, count in source_counts.items():
                    report.append(f"  {source}: {count} 条 ({count/len(df)*100:.1f}%)")
                report.append("")
            
            # 特征统计
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            
            for col in numeric_columns:
                if col in df.columns:
                    data = df[col].dropna()
                    if len(data) > 0:
                        report.append(f"{col} 统计:")
                        report.append(f"  数量: {len(data)}")
                        report.append(f"  均值: {data.mean():.6f}")
                        report.append(f"  标准差: {data.std():.6f}")
                        report.append(f"  最小值: {data.min():.6f}")
                        report.append(f"  最大值: {data.max():.6f}")
                        report.append("")
            
            # 保存报告
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report))
            
            logger.info(f"数据集报告已保存到: {report_filename}")
            
        except Exception as e:
            logger.error(f"生成数据集报告失败: {e}")


def main():
    """
    主函数
    """
    print("历史订单薄数据收集器")
    print("="*50)
    
    collector = HistoricalOrderBookCollector("ETHUSDT")
    
    print("选择操作:")
    print("1. 开始收集实时数据 (用于构建训练集)")
    print("2. 构建训练数据集 (基于已收集的数据)")
    print("3. 获取历史K线数据并估算特征")
    print("4. 综合构建训练数据集")
    
    try:
        choice = input("请选择 (1-4): ").strip()
        
        if choice == "1":
            hours = int(input("收集时长（小时，建议24-72小时）: "))
            collector.collect_realtime_for_training(duration_hours=hours)
            
        elif choice == "2":
            days = int(input("使用多少天内的数据 (默认7天): ") or "7")
            df = collector.build_training_dataset(days_back=days)
            if not df.empty:
                collector.save_training_dataset(df)
            
        elif choice == "3":
            interval = input("K线间隔 (1m/5m/15m/1h, 默认5m): ") or "5m"
            limit = int(input("获取数量 (默认1000): ") or "1000")
            
            klines = collector.get_historical_klines(interval=interval, limit=limit)
            if klines:
                print(f"获取到 {len(klines)} 条K线数据")
                
                # 估算特征
                features = []
                for kline in klines:
                    feature = collector.estimate_orderbook_from_kline(kline)
                    if feature:
                        features.append(feature)
                
                if features:
                    df = pd.DataFrame(features)
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"demo/estimated_features_{timestamp}.csv"
                    df.to_csv(filename, index=False)
                    print(f"估算特征已保存到: {filename}")
            
        elif choice == "4":
            print("开始综合构建训练数据集...")
            df = collector.build_training_dataset(days_back=30)  # 使用30天数据
            if not df.empty:
                collector.save_training_dataset(df)
                print("训练数据集构建完成!")
            else:
                print("没有可用数据，请先收集实时数据")
        
        else:
            print("无效选择")
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")


if __name__ == "__main__":
    main()