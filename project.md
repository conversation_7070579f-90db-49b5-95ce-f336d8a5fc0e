# ETH交易预测项目

## 项目概述

这是一个基于机器学习的以太坊(ETH)价格预测和回测系统。项目使用LightGBM模型预测价格在指定时间内是先涨还是先跌，并提供完整的回测功能。

**简化说明**: 项目已简化，移除了CSV数据源支持，现在只支持SQLite数据库作为数据源。

## 项目结构

### 核心文件

- **train.py** - 单币种模型训练脚本
- **train_multi.py** - 多币种联合训练脚本 (新增)
- **backtest.py** - 回测脚本
- **data_loader.py** - 公共数据加载模块（新增）
- **data_providers.py** - 数据提供者（只支持SQLite）
- **model_utils.py** - 模型工具函数（只支持SQLite）
- **config.json** - 配置文件
- **coin_data.db** - SQLite数据库

### 辅助文件

- **analyze_backtest.py** - 回测结果分析
- **get_coin_history.py** - 获取历史数据
- **onekey.py** - 一键运行脚本

### 实时交易模块 (trade/)

- **real_main.py** - 实时交易主程序 (支持SuperTrend过滤)
- **real_data.py** - 实时行情数据获取
- **real_signal.py** - 实时信号生成 (集成SuperTrend过滤)
- **real_manager.py** - 实时资金管理
- **supertrend_utils.py** - SuperTrend计算工具 (新增)
- **database_manager.py** - 数据库管理
- **order_manager.py** - 订单管理

### 输出目录

- **models/** - 存放训练好的模型和结果

## 数据结构

### SQLite数据库结构

数据库表名格式: `{symbol}_{interval}_{market}`
例如: `ETHUSDT_15min_spot`

表结构:
```sql
CREATE TABLE ETHUSDT_15min_spot (
    timestamp INTEGER PRIMARY KEY,     -- Unix时间戳
    open REAL,                         -- 开盘价
    high REAL,                         -- 最高价
    low REAL,                          -- 最低价
    close REAL,                        -- 收盘价
    volume REAL,                       -- 成交量
    close_time INTEGER,                -- 收盘时间
    quote_volume REAL,                 -- 报价资产成交量
    trade_count INTEGER,               -- 成交笔数
    taker_buy_base_volume REAL,        -- 主动买入成交量(基础资产)
    taker_buy_quote_volume REAL,       -- 主动买入成交量(报价资产)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 配置文件结构

```json
{
  "output_dir": "models",
  "coin_configs": {
    "ETH": {
      "db_path": "coin_data.db",
      "api_symbol": "ETHUSDT",
      "display_name": "ETH/USDT",
      "timeframe_minutes": 15,
      "up_threshold": 0.05,
      "down_threshold": 0.05,
      "max_lookforward_minutes": 1440,
      "model_basename": "eth_15m",
      "price_multiplier": 1.0
    }
  }
}
```

## 核心流程

### 1. 数据加载流程

```
用户输入参数 → data_loader.py → data_providers.py → SQLite数据库
                     ↓
              返回DataFrame或DataProvider实例
```

**关键函数**:
- `load_data_for_training()` - 训练时加载数据
- `load_data_for_backtest()` - 回测时创建数据提供者
- `create_data_source_config()` - 创建数据源配置

### 2. 模型训练流程

```
数据加载 → 特征计算 → 标签生成 → 数据分割 → 时序交叉验证 → 超参数优化 → 模型训练 → 模型保存
```

**关键步骤**:
1. 使用`data_loader.load_data_for_training()`加载数据
2. 使用`model_utils.calculate_features()`计算技术指标特征
3. 使用`create_percentage_target()`生成标签（先涨还是先跌）
4. 分割为训练集、验证集、测试集
5. **🆕 时序交叉验证**: 使用`TimeSeriesSplit`进行超参数优化
6. 使用最佳参数训练LightGBM模型并进行概率校准
7. 保存模型和配置文件

**时序交叉验证特性**:
- 防止数据泄露：确保训练集永远在验证集之前
- 自动超参数搜索：189种参数组合的网格搜索
- 稳定性评估：多个时间窗口的模型性能验证
- 自定义评估指标：基于交易得分的实用评估

### 3. 回测流程

```
模型加载 → 数据提供者创建 → 增量数据处理 → 预测生成 → 结果分析
```

**关键步骤**:
1. 加载训练好的模型和配置
2. 使用`data_loader.load_data_for_backtest()`创建数据提供者
3. 增量处理历史数据，模拟实时交易环境
4. 对每个时间点进行预测并跟踪结果
5. 生成详细的交易日志和分析报告

## 特征工程

### 🆕 主动买卖量特征 (2025-09-07)

基于 `taker_buy_base_volume` 和 `taker_buy_quote_volume` 数据，我们新增了一套强大的主动买卖量特征，显著提升了模型性能。

#### 核心特征

1. **基础比例特征**
   - `taker_buy_ratio`: 主动买入占总量比例
   - `taker_sell_ratio`: 主动卖出占总量比例
   - `buy_sell_ratio`: 买卖力量比 (买入量/卖出量)
   - `buy_sell_diff`: 买卖差异 (买入比例 - 卖出比例)

2. **移动平均和偏离度**
   - `taker_buy_ratio_ma_{120,360,720}`: 不同时间窗口的买入比例移动平均
   - `taker_buy_ratio_dev_{120,360,720}`: 当前买入比例相对历史平均的偏离
   - `buy_sell_ratio_ma_{120,360,720}`: 买卖力量比的移动平均
   - `buy_sell_ratio_dev_{120,360,720}`: 买卖力量比的偏离度

3. **波动性特征**
   - `taker_buy_ratio_std_{120,360}`: 买入比例的标准差
   - `buy_sell_ratio_std_{120,360}`: 买卖力量比的标准差

4. **动量特征**
   - `taker_buy_ratio_change_1`: 买入比例的1期变化
   - `buy_sell_ratio_change_1`: 买卖力量比的1期变化

5. **市场状态标识**
   - `extreme_buy`: 极端买入状态 (买入比例 > 0.7)
   - `extreme_sell`: 极端卖出状态 (买入比例 < 0.3)
   - `balanced_trading`: 平衡交易状态 (0.4 ≤ 买入比例 ≤ 0.6)

6. **价格交互特征**
   - `price_vs_buy_pressure`: 价格变化与买入压力的交互项

#### 性能提升

通过对比测试，包含主动买卖量特征的模型相比传统特征模型：
- **准确率提升**: +7.76% (从85.74%提升到92.39%)
- **特征重要性**: 主动买卖量特征占据了Top 20特征中的多个位置
- **最重要特征**: `buy_sell_ratio_std_360` (重要性: 6.20%)

#### 使用方法

```python
# 从数据库加载包含主动买卖量的数据
from model_utils_815 import load_and_prepare_data_from_db, calculate_features

df = load_and_prepare_data_from_db(db_path, table_name)
df_with_features = calculate_features(df, timeframe=5)
```

### 技术指标特征

1. **时间特征**: 小时、星期几的正弦/余弦编码
2. **K线特征**: 基于ATR标准化的实体大小、影线长度等
3. **动量特征**: 不同时间窗口的价格回报率
4. **趋势特征**: 移动平均线及其比率
5. **波动率特征**: 价格标准差与价格的比率
6. **震荡器**: RSI、布林带等
7. **成交量特征**: 成交量移动平均、VWAP等

### 特征标准化

使用ATR（平均真实波幅）对K线特征进行标准化，提高模型在不同市场条件下的稳健性。

## 预测目标

模型预测在指定时间窗口内（如24小时），价格是先达到上涨目标（如+5%）还是先达到下跌目标（如-5%）。

- **标签1**: 先涨到目标价位
- **标签0**: 先跌到目标价位
- **无标签**: 在时间窗口内都没有达到目标价位

## 使用方法

### 单币种训练

```bash
# 训练单个币种模型
python train.py --coin ETH --mode train

# 验证单个币种模型
python train.py --coin ETH --mode validate
```

### 多币种联合训练 (新增)

```bash
# 使用默认参数训练所有 15 分钟现货数据
python train_multi.py --db-path coin_data.db --interval 15min --market spot

# 自定义训练参数
python train_multi.py \
    --db-path coin_data.db \
    --interval 15min \
    --market spot \
    --up-threshold 0.03 \
    --down-threshold 0.03 \
    --max-lookforward-minutes 720 \
    --model-file multi_coin_model.joblib

# 指定时间范围训练
python train_multi.py \
    --db-path coin_data.db \
    --interval 15min \
    --market spot \
    --start-time 2025-08-01 \
    --end-time 2025-08-25
```

### 运行回测

```bash
python backtest.py --coin ETH
```

### 自定义参数

```bash
# 自定义数据库和交易对
python train.py --coin ETH --db-path custom.db --symbol ETHUSDT --interval 15m

# 回测时设置止损和开始时间
python backtest.py --coin ETH --stop-loss 2.5 --start-time "2024-01-01"
```

## 输出文件

### 训练输出

- `{model_basename}_model.joblib` - 训练好的模型
- `{model_basename}_config.json` - 模型配置
- `feature_importance_{model_basename}.csv` - 特征重要性
- `test_results_{model_basename}.csv` - 测试集结果

### 回测输出

- `{coin}_backtest_log.csv` - 详细交易日志
- `{coin}_backtest_log_report.txt` - 回测报告
- `{coin}_backtest_log_analysis.png` - 收益分析图表

## 简化改进

1. **移除CSV支持**: 只保留SQLite数据源，简化代码复杂度
2. **提取公共模块**: 创建`data_loader.py`减少代码重复
3. **统一接口**: 标准化数据加载和配置管理
4. **简化参数**: 移除不必要的命令行参数

## 增强特征工程系统

### 核心改进 (2025-08-07)

我们实施了多时间尺度和市场状态感知的增强特征工程系统，显著提升了模型对市场变化的鲁棒性。

### 主要特性

1. **多时间尺度特征**
   - 集成日线数据 (`ETHUSDT_1day_spot`)
   - 跨时间尺度特征对齐和整合
   - 从分钟级到日线级的信息融合

2. **市场状态特征 (36个)**
   - 趋势强度: 多周期移动平均线斜率、价格相对位置、均线排列
   - 波动率状态: 历史波动率、波动率分位数、ATR相对水平
   - 市场结构: 高低点突破、价格区间位置
   - 成交量特征: 成交量比率、成交量突增检测
   - 市场情绪: VIX-like恐慌贪婪指数

3. **增强技术指标**
   - 价格动量和加速度指标
   - GARCH-like波动率聚类特征
   - 市场微观结构特征（价格跳跃、成交量价格趋势）

### 特征数量提升

- **之前**: 55个特征
- **现在**: 116个特征（93个用于训练）
- **新增**: 36个市场状态特征 + 增强技术指标

### 特征分组
- 时间特征: 6个
- K线形态特征: 4个
- 动量特征: 11个
- 趋势特征: 8个
- 波动率特征: 24个
- 震荡器特征: 7个
- 成交量特征: 17个
- **市场状态特征: 36个** (新增)
- **微观结构特征: 2个** (新增)

## 时序交叉验证系统

### 核心改进 (2025-08-07)

我们实施了基于 `sklearn.model_selection.TimeSeriesSplit` 的时序交叉验证系统，这是解决时间序列机器学习模型过拟合的关键改进。

### 主要特性

1. **防止数据泄露**
   - 使用 `TimeSeriesSplit` 确保训练集永远在验证集之前
   - 严格遵循时间顺序，避免用未来数据预测过去

2. **自动超参数优化**
   - 网格搜索189种参数组合
   - 包括学习率、树深度、叶子节点数等关键参数
   - 基于交易得分的实用评估指标

3. **稳定性评估**
   - 多个时间窗口验证模型性能
   - 计算平均得分和标准差
   - 识别模型在不同时期的表现差异

### 使用方法

```bash
# 启用时序交叉验证（默认）
python train.py --coin ETH --cv-splits 5

# 禁用交叉验证（快速训练）
python train.py --coin ETH --no-time-series-cv
```

### 输出文件

- `cv_results_{MODEL_BASENAME}.json`: 详细的交叉验证结果
- 模型配置中包含最佳参数和验证得分

### 性能影响

- **计算时间**: 增加约10-20倍（取决于参数搜索空间）
- **内存使用**: 需要存储多个模型的中间结果
- **模型质量**: 显著提高泛化能力和稳定性

## TradingView风格回测可视化系统

### 核心功能 (2025-08-25)

我们新增了专业的TradingView风格回测信号可视化工具，能够将回测结果直观地展示在K线图上。

### 主要特性

1. **TradingView风格界面**
   - 专业的金融图表界面
   - 暗色主题，符合交易者习惯
   - 交互式操作（缩放、平移、悬停）

2. **多层次图表布局**
   - 主图：K线图 + 交易信号标记
   - 中图：成交量柱状图
   - 下图：账户资金曲线

3. **智能信号标记**
   - 🔺 绿色向上三角形：成功的买入信号
   - 🔻 红色向下三角形：成功的卖出信号
   - 🔺 黄色向上三角形：失败的买入信号
   - 🔻 橙色向下三角形：失败的卖出信号
   - 📏 虚线：连接交易开始和结束点

4. **详细统计信息**
   - 实时显示胜率、收益率、最大回撤
   - 信心度分析和方向分析
   - 交易摘要统计

### 核心文件

- **tradingview_backtest_visualizer.py** - 主要可视化工具 (标准模式)
- **tradingview_fast_visualizer.py** - 高性能可视化工具 (大数据专用)
- **tradingview_dynamic_visualizer.py** - 动态加载可视化工具 (零数据丢失)
- **example_tradingview_visualization.py** - 使用示例脚本
- **example_dynamic_visualization.py** - 动态加载演示脚本
- **README_tradingview_visualization.md** - 详细使用说明
- **性能优化使用指南.md** - 性能优化指南
- **动态加载可视化使用指南.md** - 动态加载使用指南

### 使用方法

```bash
# 动态加载模式 (推荐，零数据丢失)
python tradingview_dynamic_visualizer.py --coin ETH --interval 15m

# 标准模式 (小到中等数据量)
python tradingview_backtest_visualizer.py --coin ETH --interval 15m

# 快速模式 (中等到大数据量)
python tradingview_backtest_visualizer.py \
    --coin ETH --interval 15m \
    --fast --max-points 2000

# 高性能模式 (大数据量专用)
python tradingview_fast_visualizer.py \
    --coin ETH --interval 15m \
    --max-days 30 --max-signals 500

# 多时间框架动态分析
python tradingview_dynamic_visualizer.py --coin ETH --interval 5m --port 5001
python tradingview_dynamic_visualizer.py --coin ETH --interval 15m --port 5002
python tradingview_dynamic_visualizer.py --coin ETH --interval 1h --port 5003
```

### 输出文件

- `tradingview_{coin}_{interval}_backtest.html` - 交互式图表文件
- 控制台输出详细的统计摘要

### 性能优化特性 (2025-08-25)

1. **多模式支持**
   - 标准模式：完整功能，适合小到中等数据量
   - 快速模式：激进采样，适合中等到大数据量
   - 高性能模式：专为大数据量优化

2. **智能数据采样**
   - 自适应采样算法：保留关键数据点
   - 分层采样：最近数据保持更高密度
   - 极值保留：保留价格和成交量的极值点

3. **信号智能过滤**
   - 优先级排序：成功交易 > 大盈亏交易 > 最近交易
   - 数量限制：自动限制信号数量以提高性能
   - 重要性筛选：只显示最有价值的交易信号

4. **渲染优化**
   - 减少DOM元素：限制图表元素数量
   - 简化布局：高性能模式使用双层布局
   - 连接线优化：只显示最重要的交易连线

### 动态加载架构 (2025-08-25)

**革命性的零数据丢失方案**，采用类似交易所K线图的动态加载架构：

1. **Web服务架构**
   - Flask后端服务器：提供RESTful API
   - 前端JavaScript：动态请求和渲染数据
   - 实时通信：支持按需数据加载

2. **API设计**
   ```
   GET /api/kline     - K线数据接口
   GET /api/signals   - 交易信号接口
   GET /api/timerange - 时间范围接口
   GET /api/config    - 配置信息接口
   ```

3. **智能加载策略**
   - 按需加载：只加载当前视图范围的数据
   - 智能缓存：已加载数据自动缓存
   - 自动扩展：缩放时自动加载更多数据

4. **零数据丢失**
   - 保留所有原始数据的完整性
   - 无采样损失，无细节遗漏
   - 支持任意时间范围的精确分析

### 技术实现

- **前端**: Plotly.js 交互式图表 + JavaScript动态加载
- **后端**: Flask Web服务器 + Python数据处理
- **数据源**: SQLite K线数据 + CSV回测日志
- **图表类型**: Candlestick + Bar + Line + Scatter
- **架构模式**: RESTful API + 动态加载 + 智能缓存

## 反向平仓功能 (2025-08-26)

### 功能概述

在 `backtest_money_quick.py` 中新增了反向平仓模式，这是一个可选的平仓策略。当启用此模式时，系统会在检测到与当前持仓方向相反的新信号时自动平仓。

### 核心特性

1. **智能信号检测**
   - 实时监控每个K线的新信号
   - 检测与当前持仓方向相反的信号
   - 优先级高于传统止损检查

2. **触发条件**
   - **看涨仓位**: 当触发看跌信号时自动平仓
   - **看跌仓位**: 当触发看涨信号时自动平仓

3. **统计分类**
   - 结果代码: `-3`
   - 显示图标: 🔄
   - 单独统计类别: "反向平仓"

### 使用方法

```bash
# 启用反向平仓模式
python backtest_money_quick.py --reverse-close [其他参数...]

# 完整示例
python backtest_money_quick.py \
  --coin ETH --interval 15m --market spot \
  --db coin_data.db --initial-capital 10000 \
  --risk-per-trade 1.0 --max-active-predictions 3 \
  --quick --reverse-close \
  --start-time "2024-01-01" --end-time "2024-02-01"
```

### 实际效果

在2024年1月ETH 15分钟数据回测中：
- 总预测数: 173
- 反向平仓: 55次 (32%)
- 总收益率: +14.27%

### 优势分析

1. **趋势跟随**: 快速响应市场趋势变化
2. **风险控制**: 提供额外的风险控制机制
3. **实时性**: 基于模型信号而非价格变化
4. **灵活性**: 完全可选，不影响原有策略

### 相关文件

- **反向平仓功能说明.md** - 详细功能说明文档
- **test_reverse_close.py** - 功能测试脚本

## 可选退出机制系统 (2025-08-26)

### 功能概述

在反向平仓功能的基础上，进一步实现了完全可选的退出机制系统。现在支持四种退出机制的自由组合：**止盈**、**止损**、**超时**、**反向平仓**。

### 四种退出机制

1. **止盈 (Take Profit)**
   - 当价格达到目标价格时平仓
   - 结果代码: `1` (成功) 或 `0` (失败)
   - 默认: 启用

2. **止损 (Stop Loss)**
   - 当亏损达到预设百分比时平仓
   - 结果代码: `-2`
   - 默认: 启用（需设置参数）

3. **超时 (Timeout)**
   - 当持仓时间超过最大等待时间时平仓
   - 结果代码: `-1`
   - 默认: 启用

4. **反向平仓 (Reverse Close)**
   - 当检测到反向信号时平仓
   - 结果代码: `-3`
   - 默认: 禁用

### 退出机制优先级

1. 反向平仓 (最高优先级)
2. 止损
3. 止盈
4. 超时 (最低优先级)

### 使用方法

```bash
# 禁用特定机制
--disable-take-profit    # 禁用止盈
--disable-stop-loss      # 禁用止损
--disable-timeout        # 禁用超时

# 启用反向平仓
--enable-reverse-close   # 启用反向平仓

# 组合示例：只使用止盈和反向平仓
python backtest_money_quick.py --coin ETH --interval 15m \
  --disable-stop-loss --disable-timeout --enable-reverse-close
```

### 应用场景

- **趋势跟随**: `--disable-timeout --enable-reverse-close`
- **保守策略**: `--stop-loss 1.5 --disable-reverse-close`
- **激进策略**: `--disable-take-profit --disable-stop-loss --enable-reverse-close`
- **研究模式**: `--disable-timeout`

### 相关文件

- **可选退出机制功能说明.md** - 详细功能说明文档
- **test_exit_mechanisms.py** - 功能测试脚本

## 反向平仓功能 (2025-09-05)

### 功能概述

成功将 `backtest_money.py` 中的 `--enable-reverse-close` 功能移植到 `backtest_money_quick.py`，并增加了可控制的最小仓位数阈值参数。

### 核心特性

1. **智能反向平仓**
   - 当模型预测出现反向信号时自动平仓
   - 可设置最小同向仓位数阈值，避免过度频繁平仓
   - 优先级高于止损和超时机制

2. **风险控制机制**
   - **最小仓位数控制**：只有当同向仓位数 ≥ 设定阈值时才触发反向平仓
   - **方向检测**：看涨仓位遇到看跌信号时平仓，反之亦然
   - **资金保护**：避免过度集中持仓的风险

3. **新增命令行参数**
   - `--enable-reverse-close`：启用反向平仓功能
   - `--reverse-close-min-positions N`：设置触发反向平仓的最小同向仓位数（默认：1）

### 使用方法

```bash
# 启用反向平仓，最小1笔同向单时触发
python backtest_money_quick.py --coin ETH --interval 15m --quick \
  --enable-reverse-close --reverse-close-min-positions 1

# 启用反向平仓，最小10笔同向单时触发（更保守）
python backtest_money_quick.py --coin ETH --interval 15m --quick \
  --enable-reverse-close --reverse-close-min-positions 10
```

### 实际应用场景

1. **风险分散**：当持有多笔同向仓位时，遇到反向信号及时平仓
2. **趋势转换**：快速响应市场趋势变化，减少损失
3. **资金管理**：避免过度集中持仓，提高资金利用效率

### 技术实现

1. **信号检测**：在每个K线周期实时获取当前预测信号
2. **仓位统计**：动态统计当前同向活跃仓位数量
3. **条件判断**：同向仓位数 ≥ 阈值 且 出现反向信号时触发
4. **平仓执行**：标记为反向平仓（result = -3），更新统计信息

### 输出示例

```
🔄 反向平仓: 已启用 (≥10笔同向单时触发反向平仓)
...
[时间] 预测完成: 先涨... -> 反向平仓🔄, 得分: -0.85, 盈亏: $-8.50
...
总预测数: 15, 成功: 6, 失败: 4, 超时: 2, 反向平仓: 3
```

### 相关文件

- **backtest_money_quick.py** - 主要回测脚本（已更新）
- **backtest_money_log_quick.csv** - 回测日志（包含反向平仓记录）

## 最大盈利跟踪功能 (2025-09-04)

### 功能概述

在 `backtest_money_quick.py` 中新增了 MaxProfitPct 跟踪功能，与现有的 MaxLossPct 功能对应，用于记录每个预测期间的最大盈利信息。

### 核心特性

1. **实时盈利跟踪**
   - 在每个K线周期实时更新最大盈利记录
   - 记录最大盈利百分比、价格和时间戳
   - 与最大亏损跟踪逻辑完全对应

2. **新增字段**
   - **MaxProfitPct**: 最大盈利百分比
   - **MaxProfitPrice**: 达到最大盈利时的价格
   - **MaxProfitTimestamp**: 达到最大盈利的时间戳

3. **智能计算逻辑**
   - **看涨预测**: 当价格上涨时记录盈利
   - **看跌预测**: 当价格下跌时记录盈利
   - 自动计算绝对值百分比

### 数据输出

在回测日志CSV文件中新增三列：
```csv
MaxProfitPct,MaxProfitPrice,MaxProfitTimestamp
2.32,2514.7400,2025-05-24 07:30:00 UTC+8
```

### 控制台输出

预测完成时显示最大盈利信息：
```
[时间] 预测完成: 先涨... -> 成功✅, 得分: +1.16, 盈亏: $+10.52,
当前资金: $918.92, 最大亏损: 0.43%, 最大盈利: 2.32%
```

### 实际应用价值

1. **风险分析**: 了解预测期间的最大盈利潜力
2. **策略优化**: 分析是否应该设置动态止盈
3. **性能评估**: 评估模型捕捉盈利机会的能力
4. **交易心理**: 了解错失的最大盈利机会

### 使用方法

功能自动启用，无需额外参数：
```bash
python backtest_money_quick.py --coin ETH --interval 15m --quick
```

### 相关文件

- **backtest_money_quick.py** - 主要回测脚本（已更新）
- **backtest_money_log_quick.csv** - 回测日志（包含新字段）

## SuperTrend实时交易功能

### 功能概述

在 `trade/real_main.py` 中新增了 `--use-supertrend` 功能，使用1000条1小时K线数据实时计算SuperTrend指标，为实时交易提供趋势过滤。

### 核心特性

- ✅ 使用1000条30分钟K线数据实时计算SuperTrend
- ✅ 优化参数：ATR周期13，倍数3.8，30分钟K线
- ✅ 每3分钟自动更新SuperTrend数据 (确保5分钟主信号时数据最新)
- ✅ 使用倒数第二根已确定的K线，确保信号稳定
- ✅ SuperTrend看涨时只做多，看跌时只做空
- ✅ 与现有时间过滤功能完全兼容
- ✅ 完整的日志记录和错误处理
- ✅ 降级处理：无法获取SuperTrend数据时允许交易

### 使用方法

```bash
# 基础用法
python trade/real_main.py --coin ETH --use-supertrend

# 自定义参数
python trade/real_main.py --coin ETH --use-supertrend \
    --supertrend-interval 15m \
    --supertrend-atr-period 10 \
    --supertrend-multiplier 3.0

# 结合时间过滤
python trade/real_main.py --coin ETH --use-supertrend \
    --use-chushou --chushou-file chushou.json
```

### 参数说明

- `--use-supertrend`: 启用SuperTrend过滤功能
- `--supertrend-interval`: K线时间间隔，默认30m
- `--supertrend-atr-period`: ATR计算周期，默认13
- `--supertrend-multiplier`: ATR倍数，默认3.8

### 过滤逻辑

| 模型预测 | SuperTrend信号 | 交易决策 |
|---------|---------------|---------|
| 看涨(1) | 看涨(1)       | ✅ 允许做多 |
| 看涨(1) | 看跌(-1)      | ❌ 跳过交易 |
| 看跌(0) | 看涨(1)       | ❌ 跳过交易 |
| 看跌(0) | 看跌(-1)      | ✅ 允许做空 |

### 相关文件

- **trade/supertrend_utils.py** - SuperTrend计算工具（新增）
- **trade/test_supertrend_real.py** - 功能测试脚本（新增）
- **trade/example_supertrend_usage.py** - 使用示例（新增）
- **trade/SUPERTREND_REAL_IMPLEMENTATION.md** - 详细实现文档（新增）

## 技术栈

- **机器学习**: LightGBM, scikit-learn
- **数据处理**: pandas, numpy
- **数据库**: SQLite3
- **可视化**: matplotlib, plotly
- **Web服务**: Flask (动态可视化)
- **技术指标**: SuperTrend (实时计算)
- **其他**: pytz (时区处理)

## 总结

本项目提供了完整的加密货币价格预测和回测解决方案，支持多种数据源、多种回测模式和详细的性能分析。新增的SuperTrend实时交易功能为实时交易系统提供了强大的趋势过滤能力，可以有效减少逆势交易，提高交易策略的整体表现。通过不断的功能增强和优化，项目已经发展成为一个功能强大、易于使用的交易策略开发平台。
